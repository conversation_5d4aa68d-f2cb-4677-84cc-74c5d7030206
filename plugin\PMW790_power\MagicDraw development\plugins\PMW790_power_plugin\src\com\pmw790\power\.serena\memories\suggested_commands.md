# Suggested Shell Commands

## Project Management Commands

### MagicDraw/Cameo Integration
```bash
# Check if MagicDraw is running
tasklist | findstr /i "magicdraw"
tasklist | findstr /i "cameo"

# Check Java processes (MagicDraw runs on Java)
jps -l | grep -i magic
jps -l | grep -i cameo
```

### Plugin Development
```bash
# Build plugin JAR
mvn clean compile package

# Copy plugin to MagicDraw plugins directory
cp target/pmw790-power-plugin.jar "%MAGICDRAW_HOME%/plugins/"

# Check plugin manifest
jar tf target/pmw790-power-plugin.jar | grep -E "(plugin.xml|MANIFEST.MF)"
```

### Log Analysis
```bash
# Find MagicDraw log files
find $HOME -name "*.log" -path "*magicdraw*" -o -path "*cameo*"

# Monitor MagicDraw logs in real-time
tail -f "%MAGICDRAW_HOME%/logs/md.log"
tail -f "$HOME/.magicdraw/*/md.log"

# Search for plugin-related log entries
grep -i "pmw790" "%MAGICDRAW_HOME%/logs/md.log"
grep -i "power" "%MAGICDRAW_HOME%/logs/md.log"
```

## Development and Debugging Commands

### Java/JVM Analysis
```bash
# Check Java version (MagicDraw typically uses specific Java versions)
java -version

# Monitor Java memory usage
jstat -gc [PID] 1s

# Generate heap dump for memory analysis
jmap -dump:format=b,file=heapdump.hprof [PID]

# Check loaded classes
jmap -histo [PID] | grep -i power
```

### Plugin Debugging
```bash
# Check if plugin classes are loaded
jmap -histo [PID] | grep -i pmw790

# Check plugin initialization logs
grep -A 10 -B 10 "PMW790Plugin" "%MAGICDRAW_HOME%/logs/md.log"

# Monitor plugin actions
grep -E "(PowerFolderAction|ReanalyzeConnectionsAction)" "%MAGICDRAW_HOME%/logs/md.log"
```

### Model Analysis
```bash
# Check model file structure (for .mdzip files)
unzip -l model.mdzip | head -20

# Search for power-related elements in model exports
grep -r -i "power" exported_model/
grep -r -i "provider\|consumer" exported_model/

# Check XML structure of exported models
xmllint --format exported_model.xml | grep -A 5 -B 5 "power"
```

## Testing Commands

### Integration Testing
```bash
# Start MagicDraw with debug options
"%MAGICDRAW_HOME%/bin/magicdraw.exe" -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005

# Check plugin loading
grep -i "plugin.*pmw790" "%MAGICDRAW_HOME%/logs/md.log"

# Verify SysML profile loading
grep -i "sysml.*profile" "%MAGICDRAW_HOME%/logs/md.log"
```

### Performance Testing
```bash
# Monitor system resources during plugin execution
# Windows:
typeperf "\\Process(magicdraw)\\% Processor Time" "\\Process(magicdraw)\\Working Set"

# Linux/Mac:
top -p [PID] -d 1

# Monitor file I/O
# Windows:  
perfmon /res

# Linux:
iotop -p [PID]
```

## Maintenance Commands

### Cache Management
```bash
# Clear MagicDraw user caches
rm -rf "$HOME/.magicdraw/*/cache/*"
rm -rf "$HOME/.magicdraw/*/temp/*"

# Windows equivalent:
del /s /q "%USERPROFILE%\.magicdraw\*\cache\*"
del /s /q "%USERPROFILE%\.magicdraw\*\temp\*"
```

### Plugin Management
```bash
# List installed plugins
ls -la "%MAGICDRAW_HOME%/plugins/" | grep -i pmw790

# Check plugin dependencies
jar tf "%MAGICDRAW_HOME%/plugins/pmw790-power-plugin.jar" | grep -E "lib/|META-INF/"

# Backup current plugin before update
cp "%MAGICDRAW_HOME%/plugins/pmw790-power-plugin.jar" "pmw790-power-plugin-backup-$(date +%Y%m%d).jar"
```

### System Diagnostics
```bash
# Check system requirements
# Windows:
systeminfo | findstr /i "memory\|processor"

# Linux/Mac:
free -h
lscpu
df -h

# Check available disk space for large models
df -h "$HOME/.magicdraw"
du -sh "$HOME/.magicdraw/*/projects/"
```

## Development Workflow Commands

### Code Analysis
```bash
# Find TODOs and FIXMEs in code
grep -r -n "TODO\|FIXME\|XXX" src/

# Check for potential memory leaks (static collections)
grep -r -n "static.*Map\|static.*List\|static.*Set" src/

# Find unused imports
find src/ -name "*.java" -exec grep -l "^import" {} \; | xargs grep -h "^import" | sort | uniq -c | sort -n
```

### Documentation Commands
```bash
# Generate JavaDoc
javadoc -d docs/api -sourcepath src/ -subpackages com.pmw790.power

# Check documentation coverage
find src/ -name "*.java" -exec grep -L "/\*\*" {} \; | wc -l
```

### Version Control Integration
```bash
# Check for large binary files that shouldn't be committed
find . -size +1M -type f

# Check code metrics
cloc src/ --by-file --csv

# Find duplicate code patterns
grep -r -n "public.*void.*Action" src/ | cut -d: -f3 | sort | uniq -c | sort -n

# Check for hardcoded strings that should be constants
grep -r -n "\"[A-Z_]*\"" src/ | grep -v "Logger\|Log\|Exception"
```

## Model Validation Commands

### SysML Model Validation
```bash
# Check for required SysML profiles in model
unzip -l model.mdzip | grep -i "profile\|sysml"

# Validate model structure for power elements
grep -r -i "stereotype.*power" exported_model.xml
grep -r -i "block.*cabinet\|block.*room" exported_model.xml

# Check for required IDP Taxonomy elements
grep -r -i "idp.*taxonomy" exported_model.xml
grep -r -i "power.*provider\|power.*consumer" exported_model.xml
```

### Connection Validation
```bash
# Check for binding connectors in model
grep -r -i "binding.*connector" exported_model.xml

# Validate constraint blocks
grep -r -i "constraint.*block" exported_model.xml

# Check for nested connector ends
grep -r -i "nested.*connector" exported_model.xml
```

## Performance Monitoring Commands

### Memory Monitoring
```bash
# Monitor heap usage over time
while true; do
  jstat -gc [PID] | tail -1
  sleep 5
done

# Track specific object allocation
jmap -histo [PID] | grep -E "PowerDiagram|ConnectionRegistry|BindingSchema"

# Monitor garbage collection
jstat -gcutil [PID] 1s
```

### Plugin Performance
```bash
# Time plugin operations
time grep -i "diagram.*creation.*complete" "%MAGICDRAW_HOME%/logs/md.log"

# Monitor file system usage during operations
# Linux:
strace -e trace=file -p [PID] 2>&1 | grep -i "\.mdzip\|\.xml"

# Windows:
# Use Process Monitor (procmon.exe) to track file access
```

### Database/Model Performance
```bash
# Check model file sizes
ls -lh *.mdzip *.xml

# Monitor model loading times
grep -i "project.*opened\|project.*loaded" "%MAGICDRAW_HOME%/logs/md.log" | tail -10

# Check for model corruption indicators
grep -i "error\|exception\|corrupt" "%MAGICDRAW_HOME%/logs/md.log" | tail -20
```

## Deployment Commands

### Plugin Distribution
```bash
# Create plugin distribution package
mkdir -p dist/pmw790-power-plugin
cp target/pmw790-power-plugin.jar dist/pmw790-power-plugin/
cp README.md INSTALL.md dist/pmw790-power-plugin/
zip -r pmw790-power-plugin-v1.0.zip dist/pmw790-power-plugin/
```

### Installation Verification
```bash
# Verify plugin installation
test -f "%MAGICDRAW_HOME%/plugins/pmw790-power-plugin.jar" && echo "Plugin installed" || echo "Plugin not found"

# Check plugin activation
grep -i "pmw790.*init" "%MAGICDRAW_HOME%/logs/md.log" | tail -5

# Verify context menu registration
grep -i "browser.*configurator" "%MAGICDRAW_HOME%/logs/md.log" | grep -i pmw790
```

## Troubleshooting Commands

### Common Issues
```bash
# Check for ClassNotFoundException
grep -i "classnotfound" "%MAGICDRAW_HOME%/logs/md.log" | grep -i pmw790

# Check for profile loading issues
grep -i "profile.*not.*found\|profile.*load.*failed" "%MAGICDRAW_HOME%/logs/md.log"

# Check for memory issues
grep -i "outofmemory\|heap" "%MAGICDRAW_HOME%/logs/md.log"

# Check for permission issues
grep -i "permission\|access.*denied" "%MAGICDRAW_HOME%/logs/md.log"
```

### Recovery Commands
```bash
# Reset MagicDraw user settings
mv "$HOME/.magicdraw" "$HOME/.magicdraw.backup.$(date +%Y%m%d)"

# Reinstall plugin
rm "%MAGICDRAW_HOME%/plugins/pmw790-power-plugin.jar"
cp target/pmw790-power-plugin.jar "%MAGICDRAW_HOME%/plugins/"

# Clear temporary files
find "$HOME/.magicdraw" -name "*.tmp" -delete
find "$HOME/.magicdraw" -name "*.lock" -delete
```
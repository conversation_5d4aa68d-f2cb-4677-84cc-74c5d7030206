# Code Style and Conventions

## Naming Conventions

### Classes and Interfaces
- **Classes**: PascalCase (e.g., `PowerDiagramManager`, `CabinetDiagramContext`)
- **Abstract Classes**: PascalCase with descriptive suffixes (e.g., `PowerDiagramContext`)
- **Interfaces**: PascalCase with descriptive names (e.g., `DiagramContext`)
- **Inner Classes**: PascalCase, often nested for organization (e.g., `ModelAnalyzer`, `RelationshipAnalyzer`)

### Methods and Variables
- **Methods**: camelCase with descriptive names (e.g., `createParametricDiagram`, `analyzeModelConnections`)
- **Variables**: camelCase (e.g., `adjacencyMap`, `elementTypeMap`, `constraintPropertiesCache`)
- **Private Fields**: camelCase with descriptive prefixes (e.g., `roomProviderValuePropertiesCache`)
- **Boolean Methods**: Prefixed with `is`, `has`, or `can` (e.g., `isRoomContext`, `hasTargetProperty`)

### Constants and Static Fields
- **Constants**: UPPER_SNAKE_CASE (e.g., `DIAGRAM_TYPE`, `LOG_PREFIX`, `TYPE_POWER_PROVIDER`)
- **Static Collections**: UPPER_SNAKE_CASE for immutable collections (e.g., `CONNECTOR_CLASSIFIERS`)
- **Cache Names**: Descriptive names with "Cache" suffix

### Packages
- **Standard Format**: lowercase with dots (e.g., `com.pmw790.power.diagram`)
- **Logical Grouping**: Packages organized by functionality (configurators, diagram, functions, schema)

## Documentation Standards

### JavaDoc Comments
- **Public Classes**: Comprehensive class-level JavaDoc with purpose and usage
- **Public Methods**: Detailed method documentation with parameters and return values
- **Complex Logic**: Inline comments explaining algorithmic decisions
- **Example Format**:
  ```java
  /**
   * Creates a parametric diagram for power analysis of the selected element
   * Handles both cabinet blocks and room blocks
   *
   * @param project The MagicDraw project
   * @param selectedElement The selected block (cabinet or room)
   */
  ```

### Section Organization
- **Section Headers**: Use comment blocks with dashes for major sections
- **Subsection Headers**: Use `//--------------------------------------------------------------------------`
- **Method Grouping**: Related methods grouped with descriptive headers

### Code Comments
- **Complex Logic**: Explain the "why" not just the "what"
- **Performance Notes**: Document performance optimizations and caching strategies
- **Integration Points**: Explain MagicDraw/Cameo integration specifics

## Code Organization Patterns

### Class Structure
1. **Constants and Static Fields** - At the top of the class
2. **Instance Fields** - Grouped by functionality
3. **Constructors** - Public constructors first, then private
4. **Public Methods** - Main functionality methods
5. **Private Helper Methods** - Supporting methods
6. **Inner Classes** - At the end, organized by functionality

### Method Organization
- **Abstract Methods First** - In abstract classes
- **Interface Implementation** - Grouped together
- **Core Functionality** - Main business logic methods
- **Helper Methods** - Utility and support methods
- **Caching Methods** - Grouped together when extensive

### Context Classes Pattern
```java
public class SomeContext extends PowerDiagramContext {
    // Constructor with clear parameter documentation
    // Abstract method implementations
    // Context-specific public methods
    // Private helper methods
    // Caching methods
}
```

## Error Handling Standards

### Exception Handling
- **Specific Exceptions**: Catch specific exceptions when possible
- **Graceful Degradation**: Fail gracefully with informative messages
- **Resource Cleanup**: Use try-with-resources or finally blocks
- **Logging**: Always log exceptions with context

### Validation
- **Null Checks**: Defensive programming with null parameter checks
- **Pre-condition Validation**: Validate state before operations
- **Post-condition Verification**: Verify results of critical operations

### Error Reporting
- **User Messages**: Use `Utilities.Log()` for user-visible messages
- **Debug Information**: Include relevant context in error messages
- **Error Recovery**: Attempt recovery when possible

## Performance Optimization Patterns

### Caching Strategy
- **Cache Naming**: Descriptive names ending with "Cache"
- **Cache Invalidation**: Clear caches on project changes
- **Cache Hierarchies**: Organize caches by scope (project, element, property)

### Collection Usage
- **Immutable Returns**: Return unmodifiable collections for public APIs
- **Efficient Lookups**: Use HashMap for O(1) lookups
- **Batch Processing**: Process collections in batches for better performance

### Memory Management
- **WeakHashMap**: Use for caches that should be garbage collected
- **Lazy Initialization**: Initialize expensive resources only when needed
- **Resource Cleanup**: Clean up resources in project close handlers

## MagicDraw Integration Patterns

### API Usage
- **Session Management**: Use SessionManager for model modifications
- **Element Access**: Use proper element access patterns
- **Threading**: Handle UI operations on the correct thread

### Stereotype Handling
- **Caching**: Cache stereotype lookups for performance
- **Validation**: Validate stereotype availability before use
- **Profile Management**: Handle profile loading and unloading

### Diagram Creation
- **Layout Management**: Apply layouts after element creation
- **Presentation Elements**: Handle presentation element creation carefully
- **Batch Operations**: Create diagram elements in batches

## Code Quality Standards

### Method Design
- **Single Responsibility**: Each method should have one clear purpose
- **Parameter Limits**: Avoid methods with too many parameters (use context objects)
- **Return Value Consistency**: Consistent return patterns (null vs empty collections)

### Class Design
- **Cohesion**: Related functionality grouped in the same class
- **Coupling**: Minimize dependencies between classes
- **Inheritance**: Use inheritance and interfaces appropriately

### Testing Considerations
- **Testable Design**: Structure code to be testable
- **Dependency Injection**: Use dependency injection where appropriate
- **Mock-Friendly**: Design interfaces that can be easily mocked
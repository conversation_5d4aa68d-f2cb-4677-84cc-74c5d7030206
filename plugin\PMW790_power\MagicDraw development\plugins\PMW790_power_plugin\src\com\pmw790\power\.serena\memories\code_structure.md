# Code Structure Overview

The PMW790 Power Plugin is organized into a sophisticated multi-package architecture:

## Package Organization

### `com.pmw790.power.main`
- `PMW790Plugin.java` - Main plugin class with comprehensive project lifecycle management
  - Handles project opening/closing events
  - Manages cache invalidation and initialization
  - Provides project switch detection and notifications

### `com.pmw790.power.configurators`
- `PowerBrowserConfigurator.java` - Configures plugin's context menu integration
- `PowerFolderAction.java` - Creates parametric diagrams (PAR Diagram action)
- `ReanalyzeConnectionsAction.java` - Analyzes and displays power connections (Analyze Rack action)

### `com.pmw790.power.diagram`
**Core Diagram Management:**
- `PowerDiagramManager.java` - High-level diagram creation and management
- `PowerDiagramElementManager.java` - Manages diagram elements, properties, and constraint blocks
- `PowerConnectorManager.java` - Creates sophisticated binding connectors with nested path support

**Context System:**
- `DiagramContext.java` - Interface defining contract for all diagram contexts
- `PowerDiagramContext.java` - Abstract base class with shared functionality
- `CabinetDiagramContext.java` - Cabinet-specific diagram operations and caching
- `RoomDiagramContext.java` - Room-level diagram operations with multi-cabinet support
- `BindingContext.java` - Context for cabinet-level binding operations
- `RoomBindingContext.java` - Context for room-level binding operations

### `com.pmw790.power.schema`
- `BindingSchemaManager.java` - Extracts and manages binding schemas from IDP Taxonomy
- `BindingSchema.java` - Collection of binding patterns for specific block types
- `BindingPattern.java` - Individual binding patterns with support for multiple binding types

### `com.pmw790.power.functions`
- `ConnectionRegistry.java` - Comprehensive registry of model connections and relationships
  - ModelAnalyzer inner class for connection analysis
  - RelationshipAnalyzer inner class for power relationship tracking
- `SysMLStereotypes.java` - SysML stereotype and profile management with extensive caching
- `Utilities.java` - Comprehensive utility functions with nested utility classes
  - ModelElements inner class for model element operations
  - CabinetProperties inner class for cabinet property management

## Key Design Patterns

### 1. Context Pattern (Enhanced)
- **DiagramContext Interface**: Standardized contract for all contexts
- **PowerDiagramContext**: Abstract base with shared functionality
- **Specialized Contexts**: CabinetDiagramContext and RoomDiagramContext with specific behaviors

### 2. Registry Pattern (Advanced)
- **ConnectionRegistry**: Central registry for all model connections
- **Multi-level Analysis**: Supports room → cabinet → provider/consumer hierarchies
- **Inner Class Organization**: ModelAnalyzer and RelationshipAnalyzer for specialized analysis

### 3. Schema Pattern
- **BindingSchemaManager**: Manages extraction and caching of binding patterns
- **Pattern Normalization**: Converts between different binding pattern types
- **Schema-Driven Operations**: Uses extracted schemas to drive binding creation

### 4. Caching Strategy (Multi-Tiered)
- **Project-Specific Caches**: Separate caches per project
- **Element-Level Caching**: Properties, constraints, and relationships
- **Performance Caches**: Optimized lookups for frequent operations

### 5. Factory Pattern
- **BindingPattern Factory Methods**: Creates different types of binding patterns
- **Diagram Creation**: Factories for different diagram types

## Core Workflow (Enhanced)

### Cabinet Diagram Creation:
1. User selects cabinet in browser → PowerBrowserConfigurator shows context menu
2. User clicks "PAR Diagram" → PowerFolderAction.actionPerformed()
3. PowerDiagramManager.createParametricDiagram() analyzes element type
4. Creates CabinetDiagramContext with cached properties
5. PowerDiagramElementManager adds assets, constraints, and properties
6. PowerConnectorManager creates schema-based binding connectors
7. Applies layout and opens diagram

### Room Diagram Creation:
1. User selects room block → Same initial flow
2. PowerDiagramManager creates RoomDiagramContext
3. RoomDiagramContext processes all cabinets in room
4. Creates individual cabinet diagrams for each cabinet with providers
5. Room diagram shows cabinet relationships and room-level providers
6. PowerConnectorManager creates room-level binding connectors

### Connection Analysis:
1. User clicks "Analyze Rack" → ReanalyzeConnectionsAction.actionPerformed()
2. ConnectionRegistry analyzes model if not already done
3. Finds cabinet and its providers using registry
4. Displays detailed power analysis in console log

## Advanced Features

### Binding Connector Creation:
- **Schema-Based**: Uses extracted binding schemas from IDP Taxonomy
- **Multiple Types**: Property-to-constraint, constraint-to-constraint, constraint-to-property
- **Nested Paths**: Supports complex nested property paths for room-level connections
- **Batch Processing**: Creates connectors in optimized batches

### Multi-Level Analysis:
- **Room Level**: Analyzes entire rooms with multiple cabinets
- **Cabinet Level**: Detailed analysis of individual cabinets
- **Hierarchy Management**: Tracks parent-child relationships between providers

### Performance Optimizations:
- **Cached Property Collections**: Pre-loads and caches all cabinet properties
- **Batch Element Processing**: Processes elements in optimized batches
- **Connection Existence Caching**: Caches connector existence checks
- **Indexed Element Lookups**: Uses indexes for fast element retrieval
# PMW790 Power Plugin for MagicDraw/Cameo

## Purpose
This is a comprehensive MagicDraw/Cameo Systems Modeler plugin designed to analyze and visualize power connections in SysML models. The plugin provides advanced tools for power system analysis and visualization.

## Core Functionality

### Power Analysis & Visualization
- **Hierarchical Power Analysis**: Supports both cabinet-level and room-level power analysis
- **Parametric Diagram Creation**: Automatically creates SysML parametric diagrams showing power relationships
- **Connection Analysis**: Analyzes power connections between providers and consumers across multiple levels
- **Power Property Management**: Handles voltage, current, power consumption, power factor, and other electrical properties

### Model Analysis Capabilities
- **Multi-Level Hierarchy**: Handles room → cabinet → provider/consumer hierarchies
- **Connection Registry**: Maintains comprehensive registry of all power connections and relationships
- **Binding Schema Management**: Uses sophisticated binding schemas to connect properties to constraint blocks
- **Constraint Block Integration**: Automatically creates and connects power calculation constraint blocks

### Diagram Creation Features
- **Cabinet Diagrams**: Creates detailed power diagrams for individual cabinets
- **Room Diagrams**: Creates comprehensive room-level diagrams showing all cabinets and their power relationships
- **Binding Connectors**: Automatically creates SysML binding connectors between value properties and constraint parameters
- **Layout Management**: Applies intelligent layout algorithms to created diagrams

## Architecture Highlights

### Context-Based Design
- **DiagramContext Interface**: Provides standardized interface for different diagram contexts
- **PowerDiagramContext**: Abstract base class with shared functionality
- **CabinetDiagramContext**: Specialized context for cabinet-level operations
- **RoomDiagramContext**: Specialized context for room-level operations

### Advanced Caching System
- **Multi-Level Caching**: Caches model elements, properties, constraints, and relationships
- **Performance Optimization**: Extensive caching for improved performance on large models
- **Project-Specific Caches**: Separate caches per project to avoid cross-contamination

### Schema-Driven Binding
- **BindingSchemaManager**: Extracts binding patterns from IDP Taxonomy
- **BindingPattern**: Supports property-to-constraint, constraint-to-constraint, and constraint-to-property bindings
- **Automatic Schema Extraction**: Reads binding schemas directly from the SysML model

## Integration Points
- **Browser Context Menu**: Integrates with MagicDraw's browser through PowerBrowserConfigurator
- **Project Lifecycle Management**: Handles project opening/closing with proper cache management
- **SysML Profile Integration**: Deep integration with SysML stereotypes and profiles
- **Model Element Management**: Uses MagicDraw's ModelElementsManager for diagram creation

## Key Actions
- **PAR Diagram**: Creates parametric diagrams for power analysis
- **Analyze Rack**: Analyzes power connections and displays detailed power information

## Target Users
- **Systems Engineers**: Designing power distribution systems
- **Electrical Engineers**: Analyzing power consumption and distribution
- **Model-Based Engineers**: Using SysML for system design with power considerations
- **Integration Engineers**: Ensuring proper power budgeting and allocation